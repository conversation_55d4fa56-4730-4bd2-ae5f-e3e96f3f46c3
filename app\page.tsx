"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";

export default function Home() {
  return (
    <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <Image
          className="dark:invert"
          src="/zoey_eat.gif"
          alt="Next.js logo"
          width={180}
          height={38}
          priority
        />

        <div className="flex gap-4 items-center flex-col sm:flex-row">
            <Button onClick={() => window.location.href = "/dashboard"}>
            Go to dashboard
            </Button>
        </div>
      </main>
    </div>
  );
}
