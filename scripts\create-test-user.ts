#!/usr/bin/env bun

/**
 * Create Test User Script
 * 
 * This script creates a test user for development purposes.
 */

import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function createTestUser() {
  console.log('🚀 Creating test user...');

  const username = "testuser";
  const email = "<EMAIL>";
  const password = "password123";

  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { username }
    });

    if (existingUser) {
      console.log('ℹ️  Test user already exists');
      console.log(`Username: ${existingUser.username}`);
      console.log(`Email: ${existingUser.email}`);
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
      },
      select: {
        id: true,
        username: true,
        email: true,
        createdAt: true,
      }
    });

    console.log('✅ Test user created successfully!');
    console.log(`Username: ${user.username}`);
    console.log(`Email: ${user.email}`);
    console.log(`Password: ${password}`);
    console.log(`Created: ${user.createdAt}`);

  } catch (error) {
    console.error('❌ Failed to create test user:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createTestUser().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
