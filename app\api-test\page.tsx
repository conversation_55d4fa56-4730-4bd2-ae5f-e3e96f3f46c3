'use client';

import { useState } from 'react';

interface ApiResponse {
  success?: boolean;
  data?: unknown;
  error?: string;
  timestamp?: string;
  [key: string]: unknown;
}

export default function ApiTestPage() {
  const [responses, setResponses] = useState<Record<string, ApiResponse>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const endpoints = [
    { name: 'API Info', url: '/api' },
    { name: 'Health Check', url: '/api/health' },
    { name: 'Status', url: '/api/status' },
    { name: 'Version', url: '/api/version' }
  ];

  const testEndpoint = async (name: string, url: string) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    
    try {
      const response = await fetch(url);
      const data = await response.json();
      
      setResponses(prev => ({
        ...prev,
        [name]: {
          ...data,
          _httpStatus: response.status,
          _httpStatusText: response.statusText
        }
      }));
    } catch (error) {
      setResponses(prev => ({
        ...prev,
        [name]: {
          error: error instanceof Error ? error.message : 'Unknown error',
          _httpStatus: 0
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const testAllEndpoints = async () => {
    for (const endpoint of endpoints) {
      await testEndpoint(endpoint.name, endpoint.url);
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">CheffUp API Test Dashboard</h1>
        <p className="text-gray-600 mb-4">
          Test the API endpoints to ensure they&apos;re working correctly.
        </p>
        
        <button
          onClick={testAllEndpoints}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
        >
          Test All Endpoints
        </button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {endpoints.map((endpoint) => (
          <div key={endpoint.name} className="border rounded-lg p-4 bg-white shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-xl font-semibold">{endpoint.name}</h2>
              <button
                onClick={() => testEndpoint(endpoint.name, endpoint.url)}
                disabled={loading[endpoint.name]}
                className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
              >
                {loading[endpoint.name] ? 'Testing...' : 'Test'}
              </button>
            </div>
            
            <div className="mb-3">
              <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                GET {endpoint.url}
              </code>
            </div>

            {responses[endpoint.name] && (
              <div className="mt-3">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm font-medium">Response:</span>
                  <span 
                    className={`text-xs px-2 py-1 rounded ${
                      responses[endpoint.name]._httpStatus === 200 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {String(responses[endpoint.name]._httpStatus)} {String(responses[endpoint.name]._httpStatusText)}
                  </span>
                </div>
                
                <pre className="bg-gray-50 p-3 rounded text-xs overflow-auto max-h-64">
                  {JSON.stringify(responses[endpoint.name], null, 2)}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-2">API Endpoints Documentation</h3>
        <ul className="text-sm space-y-1">
          <li><strong>/api</strong> - General API information and available endpoints</li>
          <li><strong>/api/health</strong> - Basic health check with uptime and status</li>
          <li><strong>/api/status</strong> - Detailed system status including memory usage</li>
          <li><strong>/api/version</strong> - Application version and dependency information</li>
        </ul>
      </div>
    </div>
  );
}
