#!/usr/bin/env bun

/**
 * Database Migration Script
 * 
 * This script handles database migrations and table creation for the CheffUp application.
 * It uses Prisma to manage database schema and migrations.
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

const PRISMA_SCHEMA_PATH = path.join(process.cwd(), 'prisma', 'schema.prisma');

async function main() {
  console.log('🚀 Starting database migration process...');

  // Check if Prisma schema exists
  if (!existsSync(PRISMA_SCHEMA_PATH)) {
    console.error('❌ Prisma schema not found at:', PRISMA_SCHEMA_PATH);
    process.exit(1);
  }

  try {
    // Generate Prisma client
    console.log('📦 Generating Prisma client...');
    execSync('bunx prisma generate', { stdio: 'inherit' });

    // Check database connection
    console.log('🔍 Checking database connection...');
    execSync('bunx prisma db push --accept-data-loss', { stdio: 'inherit' });

    // Run migrations
    console.log('🔄 Running database migrations...');
    try {
      execSync('bunx prisma migrate dev --name init', { stdio: 'inherit' });
    } catch (error) {
      // If migration fails, try to push schema directly
      console.log('⚠️  Migration failed, trying to push schema directly...');
      execSync('bunx prisma db push', { stdio: 'inherit' });
    }

    // Seed database (if seed script exists)
    try {
      console.log('🌱 Seeding database...');
      execSync('bunx prisma db seed', { stdio: 'inherit' });
    } catch (error) {
      console.log('ℹ️  No seed script found or seeding failed, skipping...');
    }

    console.log('✅ Database migration completed successfully!');

    // Display database status
    console.log('\n📊 Database Status:');
    try {
      execSync('bunx prisma migrate status', { stdio: 'inherit' });
    } catch (error) {
      console.log('ℹ️  Database is in sync with schema');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Database Migration Script

Usage:
  bun run scripts/migrate-database.ts [options]

Options:
  --help, -h     Show this help message
  --reset        Reset database (WARNING: This will delete all data)
  --status       Show database status only

Examples:
  bun run scripts/migrate-database.ts
  bun run scripts/migrate-database.ts --status
  bun run scripts/migrate-database.ts --reset
  `);
  process.exit(0);
}

if (args.includes('--status')) {
  console.log('📊 Checking database status...');
  try {
    execSync('bunx prisma migrate status', { stdio: 'inherit' });
  } catch (error) {
    console.log('ℹ️  Database is in sync with schema');
  }
  process.exit(0);
}

if (args.includes('--reset')) {
  console.log('⚠️  WARNING: This will reset the database and delete all data!');
  console.log('Press Ctrl+C to cancel, or wait 5 seconds to continue...');
  
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  try {
    console.log('🔄 Resetting database...');
    execSync('bunx prisma migrate reset --force', { stdio: 'inherit' });
    console.log('✅ Database reset completed!');
  } catch (error) {
    console.error('❌ Database reset failed:', error);
    process.exit(1);
  }
  process.exit(0);
}

// Run main migration process
main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
