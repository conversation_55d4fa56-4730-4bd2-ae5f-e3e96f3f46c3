#!/usr/bin/env bun

/**
 * Create Sample Users Script
 * 
 * This script creates sample users with the new name/email structure.
 */

import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

const sampleUsers = [
  {
    name: "<PERSON>",
    surname: "User",
    email: "<EMAIL>",
    password: "password123"
  },
  {
    name: "<PERSON>",
    surname: "<PERSON><PERSON>",
    email: "<EMAIL>",
    password: "password123"
  },
  {
    name: "<PERSON>",
    surname: "<PERSON>",
    email: "<EMAIL>",
    password: "password123"
  }
];

async function createSampleUsers() {
  console.log('👥 Creating sample users...');

  try {
    for (const userData of sampleUsers) {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (existingUser) {
        console.log(`ℹ️  User ${userData.email} already exists, skipping...`);
        continue;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12);

      // Create user
      const user = await prisma.user.create({
        data: {
          name: userData.name,
          surname: userData.surname,
          email: userData.email,
          password: hashedPassword,
        },
        select: {
          id: true,
          name: true,
          surname: true,
          email: true,
          createdAt: true,
        }
      });

      console.log(`✅ Created user: ${user.name} ${user.surname} (${user.email})`);
      console.log(`   Password: ${userData.password}`);
      console.log(`   Created: ${user.createdAt}`);
      console.log('');
    }

    console.log('✅ Sample users created successfully!');
    console.log('\n📝 Login credentials:');
    sampleUsers.forEach((user, index) => {
      console.log(`${index + 1}. Email: ${user.email} | Password: ${user.password}`);
    });

  } catch (error) {
    console.error('❌ Failed to create sample users:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createSampleUsers().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
