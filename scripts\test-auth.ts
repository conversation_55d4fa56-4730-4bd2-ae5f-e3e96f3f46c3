#!/usr/bin/env bun

/**
 * Test Authentication Script
 * 
 * This script tests the authentication logic directly.
 */

import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function testAuth() {
  console.log('🔐 Testing authentication...');

  const testUsername = "testuser";
  const testPassword = "password123";

  try {
    // Find user
    console.log(`🔍 Looking for user: ${testUsername}`);
    const user = await prisma.user.findUnique({
      where: {
        username: testUsername
      }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      hasPassword: !!user.password,
      passwordLength: user.password.length
    });

    // Test password
    console.log(`🔑 Testing password: ${testPassword}`);
    const isPasswordValid = await bcrypt.compare(testPassword, user.password);
    console.log('🔑 Password valid:', isPasswordValid);

    if (isPasswordValid) {
      console.log('✅ Authentication would succeed');
    } else {
      console.log('❌ Authentication would fail');
      
      // Let's also test what the hash should be
      console.log('🔍 Testing hash generation...');
      const newHash = await bcrypt.hash(testPassword, 12);
      console.log('New hash:', newHash);
      const testNewHash = await bcrypt.compare(testPassword, newHash);
      console.log('New hash test:', testNewHash);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Test with the yuuki user too
async function testYuukiAuth() {
  console.log('\n🔐 Testing yuuki authentication...');

  const testUsername = "yuuki";
  // We don't know the password, but let's see the user data

  try {
    const user = await prisma.user.findUnique({
      where: {
        username: testUsername
      }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ Yuuki user found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      hasPassword: !!user.password,
      passwordLength: user.password.length,
      passwordStart: user.password.substring(0, 10) + '...'
    });

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the tests
testAuth()
  .then(() => testYuukiAuth())
  .catch((error) => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
