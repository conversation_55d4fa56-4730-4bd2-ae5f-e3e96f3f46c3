"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { MapPin, Check } from "lucide-react"

interface AddressData {
  street: string
  city: string
  state: string
  zipCode: string
  country: string
}

interface AddressSuggestion {
  id: string
  display_name: string
  address: AddressData
}

interface AddressInputProps {
  value: AddressData
  onChange: (address: AddressData) => void
  className?: string
  error?: string
}

export function AddressInput({ value, onChange, className, error }: AddressInputProps) {
  const [query, setQuery] = React.useState("")
  const [suggestions, setSuggestions] = React.useState<AddressSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = React.useState(false)
  const [isManual, setIsManual] = React.useState(false)
  const [isVerified, setIsVerified] = React.useState(false)

  // Mock address suggestions for demo purposes
  // In a real app, you'd integrate with Google Places API, MapBox, or similar
  const mockSuggestions = React.useMemo(() => [
    {
      id: "1",
      display_name: "123 Main St, New York, NY 10001, USA",
      address: {
        street: "123 Main St",
        city: "New York",
        state: "NY",
        zipCode: "10001",
        country: "USA"
      }
    },
    {
      id: "2", 
      display_name: "456 Oak Ave, Los Angeles, CA 90210, USA",
      address: {
        street: "456 Oak Ave",
        city: "Los Angeles", 
        state: "CA",
        zipCode: "90210",
        country: "USA"
      }
    },
    {
      id: "3",
      display_name: "789 Pine Rd, Chicago, IL 60601, USA", 
      address: {
        street: "789 Pine Rd",
        city: "Chicago",
        state: "IL", 
        zipCode: "60601",
        country: "USA"
      }
    }
  ], [])

  // Simulate API search with mock data
  React.useEffect(() => {
    if (query.length > 2) {
      const filtered = mockSuggestions.filter(suggestion =>
        suggestion.display_name.toLowerCase().includes(query.toLowerCase())
      )
      setSuggestions(filtered)
      setShowSuggestions(true)
    } else {
      setSuggestions([])
      setShowSuggestions(false)
    }
  }, [query, mockSuggestions])

  const handleSuggestionSelect = (suggestion: AddressSuggestion) => {
    onChange(suggestion.address)
    setQuery(suggestion.display_name)
    setShowSuggestions(false)
    setIsVerified(true)
  }

  const handleManualEntry = () => {
    setIsManual(true)
    setShowSuggestions(false)
    setIsVerified(false)
  }

  const handleFieldChange = (field: keyof AddressData, fieldValue: string) => {
    onChange({
      ...value,
      [field]: fieldValue
    })
    setIsVerified(false)
  }

  if (isManual) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Address</Label>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setIsManual(false)}
            className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
          >
            Use autocomplete
          </Button>
        </div>
        
        <div className="space-y-3">
          <div>
            <Label htmlFor="street" className="text-xs text-muted-foreground">Street Address</Label>
            <Input
              id="street"
              value={value.street}
              onChange={(e) => handleFieldChange("street", e.target.value)}
              placeholder="123 Main Street"
              className="mt-1"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="city" className="text-xs text-muted-foreground">City</Label>
              <Input
                id="city"
                value={value.city}
                onChange={(e) => handleFieldChange("city", e.target.value)}
                placeholder="New York"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="state" className="text-xs text-muted-foreground">State</Label>
              <Input
                id="state"
                value={value.state}
                onChange={(e) => handleFieldChange("state", e.target.value)}
                placeholder="NY"
                className="mt-1"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="zipCode" className="text-xs text-muted-foreground">ZIP Code</Label>
              <Input
                id="zipCode"
                value={value.zipCode}
                onChange={(e) => handleFieldChange("zipCode", e.target.value)}
                placeholder="10001"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="country" className="text-xs text-muted-foreground">Country</Label>
              <Input
                id="country"
                value={value.country}
                onChange={(e) => handleFieldChange("country", e.target.value)}
                placeholder="USA"
                className="mt-1"
              />
            </div>
          </div>
        </div>
        
        {error && (
          <p className="text-xs text-destructive">{error}</p>
        )}
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label htmlFor="address" className="text-sm font-medium">Address</Label>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleManualEntry}
          className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
        >
          Enter manually
        </Button>
      </div>
      
      <div className="relative">
        <div className="relative">
          <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          {isVerified && (
            <Check className="absolute right-3 top-3 h-4 w-4 text-green-600" />
          )}
          <Input
            id="address"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value)
              setIsVerified(false)
            }}
            placeholder="Start typing your address..."
            className="pl-10 pr-10"
          />
        </div>
        
        {showSuggestions && suggestions.length > 0 && (
          <Card className="absolute top-full z-10 mt-1 w-full border shadow-md">
            <div className="max-h-60 overflow-auto p-1">
              {suggestions.map((suggestion) => (
                <Button
                  key={suggestion.id}
                  type="button"
                  variant="ghost"
                  className="w-full justify-start p-3 h-auto"
                  onClick={() => handleSuggestionSelect(suggestion)}
                >
                  <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span className="text-left">{suggestion.display_name}</span>
                </Button>
              ))}
            </div>
          </Card>
        )}
      </div>
      
      {error && (
        <p className="text-xs text-destructive">{error}</p>
      )}
      
      {isVerified && (
        <div className="rounded-md bg-green-50 p-3 border border-green-200">
          <div className="flex">
            <Check className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
            <div>
              <p className="text-xs font-medium text-green-800">Address verified</p>
              <p className="text-xs text-green-700 mt-1">
                {value.street}, {value.city}, {value.state} {value.zipCode}, {value.country}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}