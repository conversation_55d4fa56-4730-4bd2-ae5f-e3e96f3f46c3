import { NextResponse } from 'next/server';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface ApiError {
  message: string;
  code?: string;
  statusCode: number;
}

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(
  data: T, 
  statusCode: number = 200,
  headers?: Record<string, string>
): NextResponse {
  const response: ApiResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(response, { 
    status: statusCode,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  });
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse(
  error: ApiError,
  headers?: Record<string, string>
): NextResponse {
  const response: ApiResponse = {
    success: false,
    error: error.message,
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(response, { 
    status: error.statusCode,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  });
}

/**
 * Handles API errors consistently
 */
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  if (error instanceof Error) {
    return createErrorResponse({
      message: error.message,
      statusCode: 500
    });
  }

  return createErrorResponse({
    message: 'An unexpected error occurred',
    statusCode: 500
  });
}

/**
 * Validates request method
 */
export function validateMethod(
  request: Request, 
  allowedMethods: string[]
): NextResponse | null {
  if (!allowedMethods.includes(request.method)) {
    return createErrorResponse({
      message: `Method ${request.method} not allowed`,
      statusCode: 405
    });
  }
  return null;
}

/**
 * CORS headers for API responses
 */
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

/**
 * No-cache headers for dynamic content
 */
export const noCacheHeaders = {
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};
