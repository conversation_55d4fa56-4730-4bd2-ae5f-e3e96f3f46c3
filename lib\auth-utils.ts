"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"

// Custom hook for authentication
export function useAuth() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const logout = async () => {
    await signOut({ 
      redirect: false,
      callbackUrl: "/login" 
    })
    router.push("/login")
    router.refresh()
  }

  return {
    user: session?.user,
    isAuthenticated: !!session,
    isLoading: status === "loading",
    logout,
  }
}

// Helper function to check if user is authenticated (client-side)
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  if (!isLoading && !isAuthenticated) {
    router.push("/login")
  }

  return { isAuthenticated, isLoading }
}
