type Code = `+${number | string}`;

export type CountryConfig = {
  /**
   * Unicode flag emoji representation (e.g., "🇫🇷" for France)
   */
  flag: string;
  /**
   * Country dialing code (e.g., "+33" for France).
   * A template literal type that ensures all country codes start
   * with a + symbol, followed by numbers or other characters.
   */
  code: Code;
  /**
   * Full country name
   */
  name: string;
  /**
   * Optional formatting pattern for phone numbers
   * @example
   * mask: '__-___-____'
   */
  mask?: string;
};

export const countryList: CountryConfig[] = [
  {
    flag: '🇦🇫',
    code: '+93',
    name: 'Afghanistan',
  },
  {
    flag: '🇦🇱',
    code: '+355',
    name: 'Albania',
  },
  {
    flag: '🇩🇿',
    code: '+213',
    name: 'Algeria',
  },
  {
    flag: '🇦🇩',
    code: '+376',
    name: 'Andorra',
  },
  {
    flag: '🇦🇴',
    code: '+244',
    name: 'Angola',
  },
  {
    flag: '🇦🇬',
    code: '******',
    name: 'Antigua and Barbuda',
  },
  {
    flag: '🇦🇷',
    code: '+54',
    name: 'Argentina',
    mask: '____ ____-____',  // Example for Argentina
  },
  {
    flag: '🇦🇲',
    code: '+374',
    name: 'Armenia',
    mask: '__-___-___',  // From original
  },
  {
    flag: '🇦🇼',
    code: '+297',
    name: 'Aruba',
  },
  {
    flag: '🇦🇺',
    code: '+61',
    name: 'Australia',
    mask: '(__) ____ ____',  // Example for Australia
  },
  {
    flag: '🇦🇹',
    code: '+43',
    name: 'Austria',
    mask: '____ ____ ____',  // Example for Austria
  },
  {
    flag: '🇦🇿',
    code: '+994',
    name: 'Azerbaijan',
  },
  {
    flag: '🇧🇸',
    code: '+1-242',
    name: 'Bahamas',
  },
  {
    flag: '🇧🇭',
    code: '+973',
    name: 'Bahrain',
  },
  {
    flag: '🇧🇩',
    code: '+880',
    name: 'Bangladesh',
  },
  {
    flag: '🇧🇧',
    code: '+1-246',
    name: 'Barbados',
  },
  {
    flag: '🇧🇾',
    code: '+375',
    name: 'Belarus',
  },
  {
    flag: '🇧🇪',
    code: '+32',
    name: 'Belgium',
    mask: '___ ___ ___',  // Example for Belgium
  },
  {
    flag: '🇧🇿',
    code: '+501',
    name: 'Belize',
  },
  {
    flag: '🇧🇯',
    code: '+229',
    name: 'Benin',
  },
  {
    flag: '🇧🇲',
    code: '+1-441',
    name: 'Bermuda',
  },
  {
    flag: '🇧🇹',
    code: '+975',
    name: 'Bhutan',
  },
  {
    flag: '🇧🇴',
    code: '+591',
    name: 'Bolivia',
  },
  {
    flag: '🇧🇦',
    code: '+387',
    name: 'Bosnia and Herzegovina',
  },
  {
    flag: '🇧🇼',
    code: '+267',
    name: 'Botswana',
  },
  {
    flag: '🇧🇷',
    code: '+55',
    name: 'Brazil',
    mask: '(__) _____-____',  // Example for Brazil
  },
  {
    flag: '🇧🇳',
    code: '+673',
    name: 'Brunei',
  },
  {
    flag: '🇧🇬',
    code: '+359',
    name: 'Bulgaria',
  },
  {
    flag: '🇧🇫',
    code: '+226',
    name: 'Burkina Faso',
  },
  {
    flag: '🇧🇮',
    code: '+257',
    name: 'Burundi',
  },
  {
    flag: '🇰🇭',
    code: '+855',
    name: 'Cambodia',
  },
  {
    flag: '🇨🇲',
    code: '+237',
    name: 'Cameroon',
  },
  {
    flag: '🇨🇻',
    code: '+238',
    name: 'Cabo Verde',
  },
  {
    flag: '🇨🇦',
    code: '+1',
    name: 'Canada',
    mask: '(__) ___-____',  // Example for Canada
  },
  {
    flag: '🇨🇫',
    code: '+236',
    name: 'Central African Republic',
  },
  {
    flag: '🇹🇩',
    code: '+235',
    name: 'Chad',
  },
  {
    flag: '🇨🇱',
    code: '+56',
    name: 'Chile',
  },
  {
    flag: '🇨🇳',
    code: '+86',
    name: 'China',
    mask: '___ ____ ____',  // Example for China
  },
  {
    flag: '🇨🇴',
    code: '+57',
    name: 'Colombia',
  },
  {
    flag: '🇰🇲',
    code: '+269',
    name: 'Comoros',
  },
  {
    flag: '🇨🇬',
    code: '+242',
    name: 'Congo (Congo-Brazzaville)',
  },
  {
    flag: '🇨🇩',
    code: '+243',
    name: 'Congo (Democratic Republic of the)',
  },
  {
    flag: '🇨🇷',
    code: '+506',
    name: 'Costa Rica',
  },
  {
    flag: '🇭🇷',
    code: '+385',
    name: 'Croatia',
  },
  {
    flag: '🇨🇺',
    code: '+53',
    name: 'Cuba',
  },
  {
    flag: '🇨🇾',
    code: '+357',
    name: 'Cyprus',
  },
  {
    flag: '🇨🇿',
    code: '+420',
    name: 'Czechia',
  },
  {
    flag: '🇩🇰',
    code: '+45',
    name: 'Denmark',
    mask: '__ __ __ __',  // Example for Denmark
  },
  {
    flag: '🇩🇯',
    code: '+253',
    name: 'Djibouti',
  },
  {
    flag: '🇩🇲',
    code: '+1-767',
    name: 'Dominica',
  },
  {
    flag: '🇩🇴',
    code: '+1-809',
    name: 'Dominican Republic',
  },
  {
    flag: '🇪🇨',
    code: '+593',
    name: 'Ecuador',
  },
  {
    flag: '🇪🇬',
    code: '+20',
    name: 'Egypt',
  },
  {
    flag: '🇸🇻',
    code: '+503',
    name: 'El Salvador',
  },
  {
    flag: '🇬🇶',
    code: '+240',
    name: 'Equatorial Guinea',
  },
  {
    flag: '🇪🇷',
    code: '+291',
    name: 'Eritrea',
  },
  {
    flag: '🇪🇪',
    code: '+372',
    name: 'Estonia',
  },
  {
    flag: '🇸🇿',
    code: '+268',
    name: 'Eswatini',
  },
  {
    flag: '🇪🇹',
    code: '+251',
    name: 'Ethiopia',
  },
  {
    flag: '🇫🇯',
    code: '+679',
    name: 'Fiji',
  },
  {
    flag: '🇫🇮',
    code: '+358',
    name: 'Finland',
  },
  {
    flag: '🇫🇷',
    code: '+33',
    name: 'France',
    mask: '__ __ __ __ __',  // From original
  },
  {
    flag: '🇬🇫',
    code: '+594',
    name: 'French Guiana',
  },
  {
    flag: '🇵🇫',
    code: '+689',
    name: 'French Polynesia',
  },
  {
    flag: '🇬🇦',
    code: '+241',
    name: 'Gabon',
  },
  {
    flag: '🇬🇲',
    code: '+220',
    name: 'Gambia',
  },
  {
    flag: '🇬🇪',
    code: '+995',
    name: 'Georgia',
    mask: '(___) ___-___',  // From original
  },
  {
    flag: '🇩🇪',
    code: '+49',
    name: 'Germany',
    mask: '___ ___ ___',  // Example for Germany
  },
  {
    flag: '🇬🇭',
    code: '+233',
    name: 'Ghana',
  },
  {
    flag: '🇬🇷',
    code: '+30',
    name: 'Greece',
  },
  {
    flag: '🇬🇱',
    code: '+299',
    name: 'Greenland',
  },
  {
    flag: '🇬🇩',
    code: '+1-473',
    name: 'Grenada',
  },
  {
    flag: '🇬🇵',
    code: '+590',
    name: 'Guadeloupe',
  },
  {
    flag: '🇬🇹',
    code: '+502',
    name: 'Guatemala',
  },
  {
    flag: '🇬🇳',
    code: '+224',
    name: 'Guinea',
  },
  {
    flag: '🇬🇼',
    code: '+245',
    name: 'Guinea-Bissau',
  },
  {
    flag: '🇬🇾',
    code: '+592',
    name: 'Guyana',
  },
  {
    flag: '🇭🇹',
    code: '+509',
    name: 'Haiti',
  },
  {
    flag: '🇭🇳',
    code: '+504',
    name: 'Honduras',
  },
  {
    flag: '🇭🇺',
    code: '+36',
    name: 'Hungary',
  },
  {
    flag: '🇮🇸',
    code: '+354',
    name: 'Iceland',
  },
  {
    flag: '🇮🇳',
    code: '+91',
    name: 'India',
    mask: '___ ___ ___',  // Example for India
  },
  {
    flag: '🇮🇩',
    code: '+62',
    name: 'Indonesia',
  },
  {
    flag: '🇮🇶',
    code: '+964',
    name: 'Iraq',
  },
  {
    flag: '🇮🇪',
    code: '+353',
    name: 'Ireland',
  },
  {
    flag: '🇮🇱',
    code: '+972',
    name: 'Israel',
  },
  {
    flag: '🇮🇹',
    code: '+39',
    name: 'Italy',
    mask: '___ ___ ___ ___',  // Example for Italy
  },
  {
    flag: '🇯🇲',
    code: '+1-876',
    name: 'Jamaica',
  },
  {
    flag: '🇯🇵',
    code: '+81',
    name: 'Japan',
    mask: '___-____-____',  // Example for Japan
  },
  {
    flag: '🇯🇴',
    code: '+962',
    name: 'Jordan',
  },
  {
    flag: '🇰🇿',
    code: '+7',
    name: 'Kazakhstan',
  },
  {
    flag: '🇰🇪',
    code: '+254',
    name: 'Kenya',
  },
  {
    flag: '🇰🇮',
    code: '+686',
    name: 'Kiribati',
  },
  {
    flag: '🇽🇰',
    code: '+383',
    name: 'Kosovo',
  },
  {
    flag: '🇰🇼',
    code: '+965',
    name: 'Kuwait',
  },
  {
    flag: '🇰🇬',
    code: '+996',
    name: 'Kyrgyzstan',
  },
  {
    flag: '🇱🇦',
    code: '+856',
    name: 'Laos',
  },
  {
    flag: '🇱🇻',
    code: '+371',
    name: 'Latvia',
  },
  {
    flag: '🇱🇧',
    code: '+961',
    name: 'Lebanon',
  },
  {
    flag: '🇱🇸',
    code: '+266',
    name: 'Lesotho',
  },
  {
    flag: '🇱🇷',
    code: '+231',
    name: 'Liberia',
  },
  {
    flag: '🇱🇾',
    code: '+218',
    name: 'Libya',
  },
  {
    flag: '🇱🇮',
    code: '+423',
    name: 'Liechtenstein',
  },
  {
    flag: '🇱🇹',
    code: '+370',
    name: 'Lithuania',
  },
  {
    flag: '🇱🇺',
    code: '+352',
    name: 'Luxembourg',
  },
  {
    flag: '🇲🇴',
    code: '+853',
    name: 'Macao',
  },
  {
    flag: '🇲🇰',
    code: '+389',
    name: 'North Macedonia',
  },
  {
    flag: '🇲🇬',
    code: '+261',
    name: 'Madagascar',
  },
  {
    flag: '🇲🇼',
    code: '+265',
    name: 'Malawi',
  },
  {
    flag: '🇲🇾',
    code: '+60',
    name: 'Malaysia',
  },
  {
    flag: '🇲🇻',
    code: '+960',
    name: 'Maldives',
  },
  {
    flag: '🇲🇱',
    code: '+223',
    name: 'Mali',
  },
  {
    flag: '🇲🇹',
    code: '+356',
    name: 'Malta',
  },
  {
    flag: '🇲🇭',
    code: '+692',
    name: 'Marshall Islands',
  },
  {
    flag: '🇲🇶',
    code: '+596',
    name: 'Martinique',
  },
  {
    flag: '🇲🇷',
    code: '+222',
    name: 'Mauritania',
  },
  {
    flag: '🇲🇺',
    code: '+230',
    name: 'Mauritius',
  },
  {
    flag: '🇾🇹',
    code: '+262',
    name: 'Mayotte',
  },
  {
    flag: '🇲🇽',
    code: '+52',
    name: 'Mexico',
    mask: '___ ___ ____',  // Example for Mexico
  },
  {
    flag: '🇫🇲',
    code: '+691',
    name: 'Micronesia',
  },
  {
    flag: '🇲🇩',
    code: '+373',
    name: 'Moldova',
  },
  {
    flag: '🇲🇨',
    code: '+377',
    name: 'Monaco',
  },
  {
    flag: '🇲🇳',
    code: '+976',
    name: 'Mongolia',
  },
  {
    flag: '🇲🇪',
    code: '+382',
    name: 'Montenegro',
  },
  {
    flag: '🇲🇦',
    code: '+212',
    name: 'Morocco',
    mask: '___ __ __ __',  // From original
  },
  {
    flag: '🇲🇿',
    code: '+258',
    name: 'Mozambique',
  },
  {
    flag: '🇲🇲',
    code: '+95',
    name: 'Myanmar',
  },
  {
    flag: '🇳🇦',
    code: '+264',
    name: 'Namibia',
  },
  {
    flag: '🇳🇷',
    code: '+674',
    name: 'Nauru',
  },
  {
    flag: '🇳🇵',
    code: '+977',
    name: 'Nepal',
  },
  {
    flag: '🇳🇱',
    code: '+31',
    name: 'Netherlands',
  },
  {
    flag: '🇳🇨',
    code: '+687',
    name: 'New Caledonia',
  },
  {
    flag: '🇳🇿',
    code: '+64',
    name: 'New Zealand',
    mask: '(___) ___-___',  // From original
  },
  {
    flag: '🇳🇮',
    code: '+505',
    name: 'Nicaragua',
  },
  {
    flag: '🇳🇪',
    code: '+227',
    name: 'Niger',
  },
  {
    flag: '🇳🇬',
    code: '+234',
    name: 'Nigeria',
  },
  {
    flag: '🇳🇺',
    code: '+683',
    name: 'Niue',
  },
  {
    flag: '🇳🇫',
    code: '+672',
    name: 'Norfolk Island',
  },
  {
    flag: '🇰🇵',
    code: '+850',
    name: 'North Korea',
  },
  {
    flag: '🇲🇵',
    code: '+1-670',
    name: 'Northern Mariana Islands',
  },
  {
    flag: '🇳🇴',
    code: '+47',
    name: 'Norway',
  },
  {
    flag: '🇴🇲',
    code: '+968',
    name: 'Oman',
  },
  {
    flag: '🇵🇰',
    code: '+92',
    name: 'Pakistan',
  },
  {
    flag: '🇵🇼',
    code: '+680',
    name: 'Palau',
  },
  {
    flag: '🇵🇸',
    code: '+970',
    name: 'Palestine',
  },
  {
    flag: '🇵🇦',
    code: '+507',
    name: 'Panama',
  },
  {
    flag: '🇵🇬',
    code: '+675',
    name: 'Papua New Guinea',
  },
  {
    flag: '🇵🇾',
    code: '+595',
    name: 'Paraguay',
  },
  {
    flag: '🇵🇪',
    code: '+51',
    name: 'Peru',
  },
  {
    flag: '🇵🇭',
    code: '+63',
    name: 'Philippines',
  },
  {
    flag: '🇵🇳',
    code: '+872',
    name: 'Pitcairn Islands',
  },
  {
    flag: '🇵🇱',
    code: '+48',
    name: 'Poland',
  },
  {
    flag: '🇵🇹',
    code: '+351',
    name: 'Portugal',
    mask: '__-___-____',  // From original
  },
  {
    flag: '🇵🇷',
    code: '+1-787',
    name: 'Puerto Rico',
  },
  {
    flag: '🇶🇦',
    code: '+974',
    name: 'Qatar',
  },
  {
    flag: '🇷🇪',
    code: '+262',
    name: 'Réunion',
  },
  {
    flag: '🇷🇴',
    code: '+40',
    name: 'Romania',
  },
  {
    flag: '🇷🇺',
    code: '+7',
    name: 'Russia',
    mask: '___ ___-__-__',  // Example for Russia
  },
  {
    flag: '🇷🇼',
    code: '+250',
    name: 'Rwanda',
  },
  {
    flag: '🇧🇱',
    code: '+590',
    name: 'Saint Barthélemy',
  },
  {
    flag: '🇸🇭',
    code: '+290',
    name: 'Saint Helena, Ascension and Tristan da Cunha',
  },
  {
    flag: '🇰🇳',
    code: '+1-869',
    name: 'Saint Kitts and Nevis',
  },
  {
    flag: '🇱🇨',
    code: '+1-758',
    name: 'Saint Lucia',
  },
  {
    flag: '🇲🇫',
    code: '+590',
    name: 'Saint Martin',
  },
  {
    flag: '🇵🇲',
    code: '+508',
    name: 'Saint Pierre and Miquelon',
  },
  {
    flag: '🇻🇨',
    code: '+1-784',
    name: 'Saint Vincent and the Grenadines',  // Uncommented
  },
  {
    flag: '🇼🇸',
    code: '+685',
    name: 'Samoa',
  },
  {
    flag: '🇦🇸',
    code: '+1',
    name: 'American Samoa',  // From original
  },
  {
    flag: '🇸🇲',
    code: '+378',
    name: 'San Marino',
  },
  {
    flag: '🇸🇹',
    code: '+239',
    name: 'Sao Tome and Principe',
  },
  {
    flag: '🇸🇦',
    code: '+966',
    name: 'Saudi Arabia',
  },
  {
    flag: '🇸🇳',
    code: '+221',
    name: 'Senegal',
  },
  {
    flag: '🇷🇸',
    code: '+381',
    name: 'Serbia',
  },
  {
    flag: '🇸🇨',
    code: '+248',
    name: 'Seychelles',
  },
  {
    flag: '🇸🇱',
    code: '+232',
    name: 'Sierra Leone',
  },
  {
    flag: '🇸🇬',
    code: '+65',
    name: 'Singapore',
    mask: '____ ____',  // Example for Singapore
  },
  {
    flag: '🇸🇽',
    code: '+1-721',
    name: 'Sint Maarten',
  },
  {
    flag: '🇸🇰',
    code: '+421',
    name: 'Slovakia',
  },
  {
    flag: '🇸🇮',
    code: '+386',
    name: 'Slovenia',
  },
  {
    flag: '🇸🇧',
    code: '+677',
    name: 'Solomon Islands',
  },
  {
    flag: '🇸🇴',
    code: '+252',
    name: 'Somalia',
  },
  {
    flag: '🇿🇦',
    code: '+27',
    name: 'South Africa',
    mask: '__ ___ ____',  // Example for South Africa
  },
  {
    flag: '🇬🇸',
    code: '+500',
    name: 'South Georgia and the South Sandwich Islands',
  },
  {
    flag: '🇰🇷',
    code: '+82',
    name: 'South Korea',
  },
  {
    flag: '🇸🇸',
    code: '+211',
    name: 'South Sudan',
  },
  {
    flag: '🇪🇸',
    code: '+34',
    name: 'Spain',
    mask: '___ ___ ___',  // Example for Spain
  },
  {
    flag: '🇱🇰',
    code: '+94',
    name: 'Sri Lanka',
  },
  {
    flag: '🇸🇩',
    code: '+249',
    name: 'Sudan',
  },
  {
    flag: '🇸🇷',
    code: '+597',
    name: 'Suriname',
  },
  {
    flag: '🇸🇯',
    code: '+47',
    name: 'Svalbard and Jan Mayen',
  },
  {
    flag: '🇸🇪',
    code: '+46',
    name: 'Sweden',
    mask: '__-___-___-__',  // From original
  },
  {
    flag: '🇨🇭',
    code: '+41',
    name: 'Switzerland',
    mask: '__ ___ __ __',  // Example for Switzerland
  },
  {
    flag: '🇸🇾',
    code: '+963',
    name: 'Syria',
  },
  {
    flag: '🇹🇼',
    code: '+886',
    name: 'Taiwan',
  },
  {
    flag: '🇹🇯',
    code: '+992',
    name: 'Tajikistan',
  },
  {
    flag: '🇹🇿',
    code: '+255',
    name: 'Tanzania',
  },
  {
    flag: '🇹🇭',
    code: '+66',
    name: 'Thailand',  // From original
  },
  {
    flag: '🇹🇱',
    code: '+670',
    name: 'Timor-Leste',
  },
  {
    flag: '🇹🇬',
    code: '+228',
    name: 'Togo',
  },
  {
    flag: '🇹🇰',
    code: '+690',
    name: 'Tokelau',
  },
  {
    flag: '🇹🇴',
    code: '+676',
    name: 'Tonga',
  },
  {
    flag: '🇹🇹',
    code: '+1-868',
    name: 'Trinidad and Tobago',
  },
  {
    flag: '🇹🇳',
    code: '+216',
    name: 'Tunisia',
  },
  {
    flag: '🇹🇷',
    code: '+90',
    name: 'Turkey',
  },
  {
    flag: '🇹🇲',
    code: '+993',
    name: 'Turkmenistan',
  },
  {
    flag: '🇹🇨',
    code: '+1-649',
    name: 'Turks and Caicos Islands',
  },
  {
    flag: '🇹🇻',
    code: '+688',
    name: 'Tuvalu',
  },
  {
    flag: '🇺🇬',
    code: '+256',
    name: 'Uganda',  // From original
  },
  {
    flag: '🇺🇦',
    code: '+380',
    name: 'Ukraine',
  },
  {
    flag: '🇦🇪',
    code: '+971',
    name: 'United Arab Emirates',
  },
  {
    flag: '🇬🇧',
    code: '+44',
    name: 'United Kingdom',
    mask: '___ ___ ___',  // Example for UK
  },
  {
    flag: '🇺🇸',
    code: '+1',
    name: 'United States',
    mask: '(__) ___-____',  // Example for US
  },
  {
    flag: '🇺🇾',
    code: '+598',
    name: 'Uruguay',
  },
  {
    flag: '🇺🇿',
    code: '+998',
    name: 'Uzbekistan',  // From original
  },
  {
    flag: '🇻🇺',
    code: '+678',
    name: 'Vanuatu',
  },
  {
    flag: '🇻🇦',
    code: '+39',
    name: 'Vatican City',
  },
  {
    flag: '🇻🇪',
    code: '+58',
    name: 'Venezuela',
  },
  {
    flag: '🇻🇳',
    code: '+84',
    name: 'Vietnam',
  },
  {
    flag: '🇻🇬',
    code: '+1-284',
    name: 'British Virgin Islands',
  },
  {
    flag: '🇻🇮',
    code: '+1-340',
    name: 'U.S. Virgin Islands',
  },
  {
    flag: '🇼🇫',
    code: '+681',
    name: 'Wallis and Futuna',
  },
  {
    flag: '🇪🇭',
    code: '+212',
    name: 'Western Sahara',
  },
  {
    flag: '🇾🇪',
    code: '+967',
    name: 'Yemen',
  },
  {
    flag: '🇿🇲',
    code: '+260',
    name: 'Zambia',
  },
  {
    flag: '🇿🇼',
    code: '+263',
    name: 'Zimbabwe',
  },
];