import { NextRequest, NextResponse } from 'next/server';
import packageJson from '../../../package.json';

export async function GET(request: NextRequest) {
  const versionInfo = {
    version: packageJson.version,
    name: packageJson.name,
    description: 'CheffUp application version information',
    buildTime: new Date().toISOString(),
    dependencies: {
      next: packageJson.dependencies.next,
      react: packageJson.dependencies.react,
      typescript: packageJson.devDependencies?.typescript
    },
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version
  };

  return NextResponse.json(versionInfo, { 
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600' // Cache for 1 hour since version info doesn't change often
    }
  });
}
