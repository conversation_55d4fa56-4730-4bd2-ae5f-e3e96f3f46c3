// Read the current version from package.json and update it

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { execSync } from 'child_process';

function updateVersion() {
  const packageJsonPath = join(__dirname, '..', 'package.json');
  
  try {
    // Read current package.json
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
    const oldVersion = packageJson.version;
    
    // Auto-detect version type from git commits
    const versionType = detectVersionType();
    
    // Update version
    const newVersion = incrementVersion(oldVersion, versionType);
    packageJson.version = newVersion;
    
    // Write back to package.json
    writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    
    console.log(`✅ Version updated from ${oldVersion} to ${newVersion} (${versionType})`);
    console.log('🔄 The new version will be automatically displayed in the app sidebar');
  } catch (error) {
    if (error instanceof Error) {
      console.error('❌ Error updating version:', error.message);
    } else {
      console.error('❌ Error updating version:', error);
    }
    process.exit(1);
  }
}

function detectVersionType(): 'major' | 'minor' | 'patch' {
  try {
    // Get commits since last tag (or all commits if no tags)
    let gitCommand: string;
    try {
      gitCommand = 'git log $(git describe --tags --abbrev=0)..HEAD --oneline';
      execSync(gitCommand, { stdio: 'pipe' });
    } catch {
      // No tags found, get last 10 commits
      gitCommand = 'git log --oneline -10';
    }
    
    const commits = execSync(gitCommand, { encoding: 'utf8' }).trim();
    
    if (!commits) {
      console.log('ℹ️ No new commits found, defaulting to patch');
      return 'patch';
    }
    
    const commitLines = commits.split('\n');
    
    // Check for breaking changes (major version)
    const hasBreakingChanges = commitLines.some(commit => 
      /BREAKING CHANGE|!:/i.test(commit) || 
      /^[a-f0-9]+\s+\w+!:/i.test(commit)
    );
    
    if (hasBreakingChanges) {
      console.log('🚨 Breaking changes detected');
      return 'major';
    }
    
    // Check for new features (minor version)
    const hasNewFeatures = commitLines.some(commit => 
      /^[a-f0-9]+\s+(feat|feature)(\(.*?\))?:/i.test(commit)
    );
    
    if (hasNewFeatures) {
      console.log('✨ New features detected');
      return 'minor';
    }
    
    // Default to patch for fixes, docs, etc.
    console.log('🔧 Bug fixes or other changes detected');
    return 'patch';
    
  } catch (error) {
    console.log('⚠️ Could not detect version type from git, defaulting to patch');
    return 'patch';
  }
}

function incrementVersion(version: string, type: 'major' | 'minor' | 'patch'): string {
  const [major, minor, patch] = version.split('.').map(Number);
  
  switch (type) {
    case 'major':
      return `${major + 1}.0.0`;
    case 'minor':
      return `${major}.${minor + 1}.0`;
    case 'patch':
    default:
      return `${major}.${minor}.${patch + 1}`;
  }
}

updateVersion();