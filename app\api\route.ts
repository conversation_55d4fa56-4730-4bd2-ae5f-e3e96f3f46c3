import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const apiInfo = {
    name: 'CheffUp API',
    version: '1.0.0',
    description: 'CheffUp application API endpoints',
    endpoints: {
      health: '/api/health',
      status: '/api/status',
      version: '/api/version'
    },
    documentation: 'https://github.com/rene-roid/cheff-up',
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(apiInfo, { 
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    }
  });
}
