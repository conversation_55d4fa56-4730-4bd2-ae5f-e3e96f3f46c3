import { NextRequest, NextResponse } from 'next/server';
import { createSuccessResponse, handleApiError, noCacheHeaders } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    const uptime = process.uptime();

    // Basic health check response
    const healthData = {
      status: 'healthy',
      uptime: `${Math.floor(uptime)}s`,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      service: 'cheff-up-api'
    };

    return createSuccessResponse(healthData, 200, noCacheHeaders);
  } catch (error) {
    return handleApiError(error);
  }
}

// Optional: Add other HTTP methods if needed
export async function HEAD(request: NextRequest) {
  // Simple HEAD request for basic connectivity check
  return new NextResponse(null, { 
    status: 200,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    }
  });
}
