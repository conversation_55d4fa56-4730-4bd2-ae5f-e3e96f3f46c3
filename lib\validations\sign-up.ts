import { z } from "zod"

// Phone number regex for basic validation (international format)
const phoneRegex = /^(\+\d{1,3}[- ]?)?\d{10}$/

export const signUpFormSchema = z.object({
  // Step 1: Personal Information
  firstName: z
    .string()
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must not exceed 50 characters")
    .regex(/^[a-zA-Z\s'-]+$/, "First name can only contain letters, spaces, hyphens, and apostrophes"),
  
  surname: z
    .string()
    .min(2, "Surname must be at least 2 characters")
    .max(50, "Surname must not exceed 50 characters")
    .regex(/^[a-zA-Z\s'-]+$/, "Surname can only contain letters, spaces, hyphens, and apostrophes"),
  
  // Step 2: Contact Information
  email: z
    .string()
    .email("Please enter a valid email address")
    .min(5, "Email must be at least 5 characters")
    .max(100, "Email must not exceed 100 characters"),
  
  age: z
    .number()
    .min(18, "You must be at least 18 years old to sign up")
    .max(120, "Please enter a valid age"),
  
  phoneNumber: z
    .string()
    .regex(phoneRegex, "Please enter a valid phone number (e.g., ************* or 1234567890)")
    .min(10, "Phone number must be at least 10 digits")
    .max(15, "Phone number must not exceed 15 digits"),
  
  // Step 3: Address Information
  address: z.object({
    street: z
      .string()
      .min(5, "Street address must be at least 5 characters")
      .max(100, "Street address must not exceed 100 characters"),
    
    city: z
      .string()
      .min(2, "City must be at least 2 characters")
      .max(50, "City must not exceed 50 characters")
      .regex(/^[a-zA-Z\s'-]+$/, "City can only contain letters, spaces, hyphens, and apostrophes"),
    
    state: z
      .string()
      .min(2, "State must be at least 2 characters")
      .max(50, "State must not exceed 50 characters")
      .regex(/^[a-zA-Z\s'-]+$/, "State can only contain letters, spaces, hyphens, and apostrophes"),
    
    zipCode: z
      .string()
      .min(4, "ZIP code must be at least 4 characters")
      .max(10, "ZIP code must not exceed 10 characters")
      .regex(/^[a-zA-Z0-9\s-]+$/, "ZIP code format is invalid"),
    
    country: z
      .string()
      .min(2, "Country must be at least 2 characters")
      .max(50, "Country must not exceed 50 characters")
      .regex(/^[a-zA-Z\s'-]+$/, "Country can only contain letters, spaces, hyphens, and apostrophes"),
  }),
})

// Type inference from the schema
export type SignUpFormData = z.infer<typeof signUpFormSchema>

// Individual step schemas for step-by-step validation
export const personalInfoSchema = signUpFormSchema.pick({
  firstName: true,
  surname: true,
})

export const contactInfoSchema = signUpFormSchema.pick({
  email: true,
  age: true,
  phoneNumber: true,
})

export const addressInfoSchema = signUpFormSchema.pick({
  address: true,
})

export type PersonalInfoData = z.infer<typeof personalInfoSchema>
export type ContactInfoData = z.infer<typeof contactInfoSchema>
export type AddressInfoData = z.infer<typeof addressInfoSchema>