"use client"

import * as React from "react"
import { GalleryVerticalEnd, ChevronRight, ChevronLeft, Check } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { AddressInput } from "@/components/ui/address-input"
import { 
  signUpFormSchema, 
  personalInfoSchema,
  contactInfoSchema,
  addressInfoSchema,
  type SignUpFormData 
} from "@/lib/validations/sign-up"

const STEPS = [
  {
    id: 1,
    title: "Personal Information",
    description: "Tell us about yourself"
  },
  {
    id: 2,
    title: "Contact Details",
    description: "How can we reach you?"
  },
  {
    id: 3,
    title: "Address",
    description: "Where are you located?"
  },
  {
    id: 4,
    title: "Review",
    description: "Confirm your details"
  }
] as const

export function SignupForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [currentStep, setCurrentStep] = React.useState(1)
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpFormSchema),
    defaultValues: {
      firstName: "",
      surname: "",
      email: "",
      age: undefined,
      phoneNumber: "",
      address: {
        street: "",
        city: "",
        state: "",
        zipCode: "",
        country: "",
      },
    },
    mode: "onChange",
  })

  const { 
    register, 
    handleSubmit, 
    formState: { errors, isValid }, 
    watch,
    setValue,
    trigger,
    getValues
  } = form

  const watchedValues = watch()

  const validateCurrentStep = async () => {
    let isStepValid = false
    
    switch (currentStep) {
      case 1:
        isStepValid = await trigger(["firstName", "surname"])
        break
      case 2:
        isStepValid = await trigger(["email", "age", "phoneNumber"])
        break
      case 3:
        isStepValid = await trigger("address")
        break
      case 4:
        isStepValid = await trigger()
        break
      default:
        break
    }
    
    return isStepValid
  }

  const nextStep = async () => {
    const isStepValid = await validateCurrentStep()
    if (isStepValid && currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const onSubmit = async (data: SignUpFormData) => {
    setIsSubmitting(true)
    try {
      // Here you would submit the form data to your API
      console.log("Form submitted:", data)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Handle successful submission
      alert("Account created successfully!")
    } catch (error) {
      console.error("Form submission error:", error)
      alert("Something went wrong. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const progress = (currentStep / STEPS.length) * 100

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                placeholder="John"
                {...register("firstName")}
                className={errors.firstName ? "border-red-500" : ""}
              />
              {errors.firstName && (
                <p className="text-sm text-red-500">{errors.firstName.message}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="surname">Surname</Label>
              <Input
                id="surname"
                placeholder="Doe"
                {...register("surname")}
                className={errors.surname ? "border-red-500" : ""}
              />
              {errors.surname && (
                <p className="text-sm text-red-500">{errors.surname.message}</p>
              )}
            </div>
          </div>
        )
      
      case 2:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email")}
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="age">Age</Label>
              <Input
                id="age"
                type="number"
                placeholder="25"
                min="18"
                max="120"
                {...register("age", { 
                  valueAsNumber: true,
                  setValueAs: (value) => value === "" ? undefined : parseInt(value, 10)
                })}
                className={errors.age ? "border-red-500" : ""}
              />
              {errors.age && (
                <p className="text-sm text-red-500">{errors.age.message}</p>
              )}
              <p className="text-xs text-muted-foreground">You must be at least 18 years old</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Phone Number</Label>
              <Input
                id="phoneNumber"
                type="tel"
                placeholder="+****************"
                {...register("phoneNumber")}
                className={errors.phoneNumber ? "border-red-500" : ""}
              />
              {errors.phoneNumber && (
                <p className="text-sm text-red-500">{errors.phoneNumber.message}</p>
              )}
            </div>
          </div>
        )
      
      case 3:
        return (
          <div className="space-y-4">
            <AddressInput
              value={watchedValues.address}
              onChange={(address) => {
                setValue("address", address, { shouldValidate: true })
              }}
              error={errors.address?.street?.message || 
                     errors.address?.city?.message || 
                     errors.address?.state?.message || 
                     errors.address?.zipCode?.message || 
                     errors.address?.country?.message}
            />
          </div>
        )
      
      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold">Review Your Information</h3>
              <p className="text-sm text-muted-foreground">
                Please review your details before creating your account
              </p>
            </div>
            
            <div className="space-y-4 rounded-lg border p-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Personal Information</h4>
                <p className="mt-1">
                  {watchedValues.firstName} {watchedValues.surname}, {watchedValues.age} years old
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Contact</h4>
                <p className="mt-1">{watchedValues.email}</p>
                <p className="text-sm">{watchedValues.phoneNumber}</p>
              </div>
              
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Address</h4>
                <p className="mt-1">
                  {watchedValues.address.street}
                  <br />
                  {watchedValues.address.city}, {watchedValues.address.state} {watchedValues.address.zipCode}
                  <br />
                  {watchedValues.address.country}
                </p>
              </div>
            </div>
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <div className="flex flex-col items-center gap-2">
        <a
          href="#"
          className="flex flex-col items-center gap-2 font-medium"
        >
          <div className="flex size-8 items-center justify-center rounded-md">
            <GalleryVerticalEnd className="size-6" />
          </div>
          <span className="sr-only">Cheff Up</span>
        </a>
        <h1 className="text-xl font-bold">Join Cheff Up</h1>
        <p className="text-center text-sm text-muted-foreground">
          Create your account in just a few steps
        </p>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>Step {currentStep} of {STEPS.length}</span>
          <span>{Math.round(progress)}% complete</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Step Indicator */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-2">
          {STEPS.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "flex size-8 items-center justify-center rounded-full border-2 text-sm",
                    currentStep >= step.id
                      ? "border-primary bg-primary text-primary-foreground"
                      : "border-muted-foreground/20 text-muted-foreground"
                  )}
                >
                  {currentStep > step.id ? (
                    <Check className="size-4" />
                  ) : (
                    step.id
                  )}
                </div>
                <div className="mt-1 text-center">
                  <p className="text-xs font-medium">{step.title}</p>
                  <p className="text-xs text-muted-foreground">{step.description}</p>
                </div>
              </div>
              {index < STEPS.length - 1 && (
                <div className="h-0.5 w-8 bg-muted-foreground/20 mt-4" />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {renderStep()}
        
        <div className="flex justify-between gap-4">
          {currentStep > 1 && (
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="size-4" />
              Previous
            </Button>
          )}
          
          <div className="flex-1" />
          
          {currentStep < STEPS.length ? (
            <Button
              type="button"
              onClick={nextStep}
              className="flex items-center gap-2"
            >
              Next
              <ChevronRight className="size-4" />
            </Button>
          ) : (
            <Button
              type="submit"
              disabled={isSubmitting || !isValid}
              className="flex items-center gap-2"
            >
              {isSubmitting ? "Creating Account..." : "Create Account"}
            </Button>
          )}
        </div>
      </form>

      <div className="text-muted-foreground text-center text-xs text-balance">
        By creating an account, you agree to our{" "}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          Terms of Service
        </a>{" "}
        and{" "}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          Privacy Policy
        </a>.
      </div>
    </div>
  )
}
