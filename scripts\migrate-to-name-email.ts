#!/usr/bin/env bun

/**
 * Migration Script: Username to Name/Email
 * 
 * This script migrates the database from username-based auth to name/email-based auth.
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function migrateUsers() {
  console.log('🔄 Migrating users from username to name/email system...');

  try {
    // First, let's see what users we have
    const existingUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
      }
    });

    console.log(`📊 Found ${existingUsers.length} existing users:`);
    existingUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.email || 'no email'})`);
    });

    if (existingUsers.length === 0) {
      console.log('ℹ️  No users to migrate');
      return;
    }

    // For each user, we need to:
    // 1. Extract name from username or email
    // 2. Set a default surname
    // 3. Ensure email is set

    for (const user of existingUsers) {
      let name = user.username;
      let surname = 'User'; // Default surname
      let email = user.email;

      // If username looks like an email, extract the name part
      if (user.username.includes('@')) {
        name = user.username.split('@')[0];
        if (!email) {
          email = user.username;
        }
      }

      // If no email, create one based on username
      if (!email) {
        email = `${user.username}@example.com`;
      }

      // Capitalize first letter of name
      name = name.charAt(0).toUpperCase() + name.slice(1);

      console.log(`🔄 Migrating user ${user.username}:`);
      console.log(`   Name: ${name}`);
      console.log(`   Surname: ${surname}`);
      console.log(`   Email: ${email}`);

      // Update the user (this will fail until we update the schema)
      // For now, just log what we would do
    }

    console.log('\n⚠️  To complete the migration:');
    console.log('1. First, we need to reset the database to apply the new schema');
    console.log('2. Then recreate users with the new structure');
    console.log('\nRun: bun run db:reset');
    console.log('Then: bun run scripts/create-sample-users.ts');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateUsers().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
