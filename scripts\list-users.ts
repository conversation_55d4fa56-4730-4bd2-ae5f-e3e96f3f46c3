#!/usr/bin/env bun

/**
 * List Users Script
 * 
 * This script lists all users in the database.
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function listUsers() {
  console.log('👥 Listing all users...');

  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (users.length === 0) {
      console.log('ℹ️  No users found in database');
      return;
    }

    console.log(`✅ Found ${users.length} user(s):`);
    console.log('');
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. User ID: ${user.id}`);
      console.log(`   Username: ${user.username}`);
      console.log(`   Email: ${user.email || 'Not provided'}`);
      console.log(`   Created: ${user.createdAt}`);
      console.log(`   Updated: ${user.updatedAt}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Failed to list users:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
listUsers().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
